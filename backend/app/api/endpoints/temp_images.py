"""
Temporary image storage endpoint for Polotno editor
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
import logging
from typing import Dict, Optional
import time

logger = logging.getLogger(__name__)

router = APIRouter()

# In-memory storage for temporary images (in production, use Redis or similar)
temp_images: Dict[str, Dict] = {}

class TempImageRequest(BaseModel):
    id: str
    imageData: str

class TempImageResponse(BaseModel):
    success: bool
    message: str

@router.post("/temp-images", response_model=TempImageResponse)
async def store_temp_image(request: TempImageRequest):
    """
    Store a temporary image for Polotno editor
    """
    try:
        logger.info(f"📥 Storing temporary image with ID: {request.id}")
        
        # Store image with timestamp for cleanup
        temp_images[request.id] = {
            'imageData': request.imageData,
            'timestamp': time.time()
        }
        
        # Clean up old images (older than 10 minutes)
        current_time = time.time()
        keys_to_remove = []
        for key, data in temp_images.items():
            if current_time - data['timestamp'] > 600:  # 10 minutes
                keys_to_remove.append(key)
        
        for key in keys_to_remove:
            del temp_images[key]
            logger.info(f"🧹 Cleaned up old temporary image: {key}")
        
        logger.info(f"✅ Temporary image stored successfully: {request.id}")
        logger.info(f"📊 Total temporary images: {len(temp_images)}")
        
        return TempImageResponse(
            success=True,
            message="Image stored successfully"
        )
        
    except Exception as e:
        logger.error(f"❌ Error storing temporary image: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error storing temporary image: {str(e)}"
        )

@router.get("/temp-images/{image_id}")
async def get_temp_image(image_id: str):
    """
    Retrieve a temporary image by ID
    """
    try:
        logger.info(f"📤 Retrieving temporary image: {image_id}")
        
        if image_id not in temp_images:
            logger.error(f"❌ Temporary image not found: {image_id}")
            raise HTTPException(
                status_code=404,
                detail="Temporary image not found"
            )
        
        image_data = temp_images[image_id]['imageData']
        
        # Remove image after retrieval (one-time use)
        del temp_images[image_id]
        logger.info(f"✅ Temporary image retrieved and deleted: {image_id}")
        
        return {
            'success': True,
            'imageData': image_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Error retrieving temporary image: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving temporary image: {str(e)}"
        )
