import React from "react";
import { motion } from "framer-motion";
import { Z<PERSON>, Rocket, ArrowLeft, LogIn } from "lucide-react";
import { <PERSON> } from "wouter";
import { useLanguage } from "@/contexts/LanguageContext";

interface HeaderProps {
  isScrolled: boolean;
}

const Header: React.FC<HeaderProps> = ({ isScrolled }) => {
  const { t } = useLanguage();

  return (
    <motion.header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled ? "py-3 bg-white/90 backdrop-blur-md shadow-xl" : "py-5 bg-transparent"
      }`}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="container mx-auto px-4 flex justify-between items-center">
        <div className="flex items-center">
          <Link href="/" className="flex items-center mr-6">
            <motion.div
              className="bg-[#3018ef] w-10 h-10 rounded-3xl flex items-center justify-center text-white shadow-xl mr-2"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <ArrowLeft size={20} />
            </motion.div>
            <span className="font-bold text-sm hidden sm:block">{t('profesionales_ia.header.back_to_home')}</span>
          </Link>
          <div className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] w-10 h-10 rounded-3xl flex items-center justify-center text-white shadow-xl mr-2">
            <Zap size={20} />
          </div>
          <span className="font-black text-xl bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">Emma</span>
        </div>
        <div className="hidden md:flex items-center space-x-6">
          <a href="#que-es" className="font-bold hover:text-[#3018ef] transition-colors">{t('profesionales_ia.header.what_is')}</a>
          <a href="#como-funciona" className="font-bold hover:text-[#3018ef] transition-colors">{t('profesionales_ia.header.how_it_works')}</a>
          <a href="#beneficios" className="font-bold hover:text-[#3018ef] transition-colors">{t('profesionales_ia.header.benefits')}</a>
          <a href="#agentes" className="font-bold hover:text-[#3018ef] transition-colors">{t('profesionales_ia.header.agents')}</a>
          <a href="#testimonios" className="font-bold hover:text-[#3018ef] transition-colors">{t('profesionales_ia.header.testimonials')}</a>
          <a href="#preguntas" className="font-bold hover:text-[#3018ef] transition-colors">{t('profesionales_ia.header.faq')}</a>
        </div>
        <div className="flex items-center space-x-3">
          <Link href="/login">
            <motion.button
              className="bg-white/20 backdrop-blur-md text-[#3018ef] font-bold py-2 px-4 text-sm rounded-3xl border border-white/30 shadow-xl hover:bg-white/30 transition-all duration-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <span className="flex items-center justify-center gap-2">
                {t('profesionales_ia.header.login')} <LogIn size={16} />
              </span>
            </motion.button>
          </Link>
          <Link href="/login">
            <motion.button
              className="bg-[#dd3a5a] text-white font-bold py-2 px-4 text-sm rounded-3xl shadow-xl hover:bg-[#c73351] transition-all duration-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <span className="flex items-center justify-center gap-2">
                {t('profesionales_ia.header.start_free')} <Rocket size={16} />
              </span>
            </motion.button>
          </Link>
        </div>
      </div>
    </motion.header>
  );
};

export default Header;
